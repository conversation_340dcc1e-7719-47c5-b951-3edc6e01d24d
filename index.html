<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sux - 绿色简约设计</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --grass-green: #4CAF50;
            --light-mint-green: #81C784;
            --deep-olive-green: #388E3C;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fffe;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 标题样式 */
        .main-title {
            font-size: 2.5rem;
            font-weight: 700;
            line-height: 1.2;
            color: var(--deep-olive-green);
            text-align: center;
            padding: 80px 0 60px;
            opacity: 0;
            transform: translateY(5px);
            animation: fadeInUp 0.5s ease-out 0.2s forwards;
        }

        /* 正文内容 */
        .content {
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.6;
            color: var(--grass-green);
            margin-bottom: 1.5rem;
            opacity: 0;
            transform: translateY(5px);
            animation: fadeInUp 0.5s ease-out forwards;
        }

        .content:nth-child(2) { animation-delay: 0.4s; }
        .content:nth-child(3) { animation-delay: 0.6s; }
        .content:nth-child(4) { animation-delay: 0.8s; }

        /* 辅助文字 */
        .auxiliary-text {
            font-size: 0.875rem;
            font-weight: 300;
            color: var(--light-mint-green);
            margin-bottom: 1rem;
            opacity: 0;
            animation: fadeInUp 0.5s ease-out 1s forwards;
        }

        /* 链接样式 */
        .interactive-link {
            color: var(--grass-green);
            text-decoration: none;
            position: relative;
            transition: color 0.3s ease;
            display: inline-block;
        }

        .interactive-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 50%;
            background-color: var(--deep-olive-green);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .interactive-link:hover {
            color: var(--deep-olive-green);
        }

        .interactive-link:hover::after {
            width: 100%;
        }

        /* 数字统计 */
        .stats-section {
            display: flex;
            justify-content: space-around;
            margin: 60px 0;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
            margin: 20px;
            opacity: 0;
            transform: scale(0.95);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--deep-olive-green);
            display: block;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1rem;
            color: var(--grass-green);
        }

        /* 特色区域 */
        .feature-section {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(129, 199, 132, 0.1));
            padding: 60px 40px;
            margin: 40px 0;
            border-radius: 15px;
            text-align: center;
        }

        .feature-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--deep-olive-green);
            margin-bottom: 30px;
            opacity: 0;
            transform: translateY(5px);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .feature-card {
            background: white;
            padding: 30px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.1);
            opacity: 0;
            transform: translateY(20px);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.2);
        }

        .feature-card h3 {
            color: var(--deep-olive-green);
            font-size: 1.2rem;
            margin-bottom: 15px;
        }

        .feature-card p {
            color: var(--grass-green);
            font-size: 0.9rem;
        }

        /* 动画定义 */
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes scaleIn {
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes countUp {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        /* 滚动触发动画 */
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
            transition: all 0.3s ease;
        }

        .scroll-reveal.revealed {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-title {
                font-size: 2rem;
                padding: 60px 0 40px;
            }
            
            .stats-section {
                flex-direction: column;
                align-items: center;
            }
            
            .feature-section {
                padding: 40px 20px;
            }
            
            .stat-number {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="main-title">Sux - 绿色生态设计理念</h1>
        
        <div class="content">
            欢迎来到 Sux 的世界，这里我们采用清新自然的绿色系列作为核心色调，
            传递生机与活力，象征着我们对可持续发展的坚持与承诺。
        </div>
        
        <div class="content">
            我们相信简约而不简单的设计哲学，通过精心选择的 
            <a href="#" class="interactive-link">Inter 字体</a> 
            和和谐的色彩搭配，为用户创造舒适的视觉体验。
        </div>
        
        <div class="content">
            每一个细节都经过精心雕琢，从文字的渐显动画到悬停时的微妙变化，
            我们致力于提升用户与页面的每一次互动体验。
        </div>
        
        <div class="auxiliary-text">
            * 本页面完全按照设计规范实现，展示了完整的色彩体系和动画效果
        </div>

        <!-- 数字统计区域 -->
        <div class="stats-section scroll-reveal">
            <div class="stat-item">
                <span class="stat-number" data-target="98">0</span>
                <span class="stat-label">用户满意度</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" data-target="156">0</span>
                <span class="stat-label">项目完成数</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" data-target="24">0</span>
                <span class="stat-label">团队成员</span>
            </div>
        </div>

        <!-- 特色功能区域 -->
        <div class="feature-section scroll-reveal">
            <h2 class="feature-title">核心特色</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🌱 生态理念</h3>
                    <p>采用草木绿作为主色调，传递可持续发展的品牌理念，营造清新自然的视觉氛围。</p>
                </div>
                <div class="feature-card">
                    <h3>✨ 简约设计</h3>
                    <p>使用 Inter 无衬线字体，减少装饰元素，通过字体本身的力量感突出主题内容。</p>
                </div>
                <div class="feature-card">
                    <h3>🎯 交互体验</h3>
                    <p>精心设计的动画效果，从文字加载到滚动交互，提升用户与页面的互动感受。</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 数字递增动画
        function animateNumbers() {
            const numbers = document.querySelectorAll('.stat-number');
            numbers.forEach(number => {
                const target = parseInt(number.getAttribute('data-target'));
                const duration = 1000;
                const step = target / (duration / 16);
                let current = 0;
                
                const timer = setInterval(() => {
                    current += step;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    number.textContent = Math.floor(current);
                }, 16);
            });
        }

        // 滚动触发动画
        function handleScrollReveal() {
            const elements = document.querySelectorAll('.scroll-reveal');
            elements.forEach(element => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;
                
                if (elementTop < window.innerHeight - elementVisible) {
                    element.classList.add('revealed');
                    
                    // 如果是统计区域，触发数字动画
                    if (element.classList.contains('stats-section')) {
                        setTimeout(animateNumbers, 200);
                    }
                    
                    // 如果是特色区域，延迟显示卡片
                    if (element.classList.contains('feature-section')) {
                        const cards = element.querySelectorAll('.feature-card');
                        cards.forEach((card, index) => {
                            setTimeout(() => {
                                card.style.animation = `scaleIn 0.3s ease-out forwards`;
                            }, index * 100);
                        });
                    }
                }
            });
        }

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            handleScrollReveal();
        });

        // 滚动事件监听
        window.addEventListener('scroll', handleScrollReveal);
    </script>
</body>
</html>
